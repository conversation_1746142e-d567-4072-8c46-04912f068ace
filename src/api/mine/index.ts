import service from '@/utils/service.ts'
import {
  userQuery,
  logoff,
  QueryPrice,
  QueryPay,
  cardItem,
  goodItem,
  payItem,
  IUserInfo,
  IAiListParams,
  IFollowList,
  IListParams,
  IFollowListItem,
  ICrystalPromotion,
  ICrystalParams,
  IGoldPriceId,
  IFaq,
  FigureListReq,
  FigureListRes,
  CountryListRes
} from './type'
import { IAiList, IAiListItem } from '../home/<USER>'

export const goods_list = () => {
  return service.get<goodItem[]>({
    url: '/apiv1/vip/goods_list'
  })
}

export const promotion_list = (data: { goods_id?: number; type?: string }) => {
  return service.post<cardItem[]>({
    url: '/apiv1/vip/promotion_list',
    data
  })
}

export const file_upload = (data: any) => {
  return service.post<{ file_path: string; url: string }>({
    url: '/apiv1/tool/file_upload',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const updateUser = (data: userQuery) => {
  return service.post({
    url: '/apiv1/user/update',
    data
  })
}
export const delete_account = (data: logoff) => {
  return service.post({
    url: '/apiv1/login/delete_account',
    data
  })
}
export const confirmDeletet = () => {
  return service.post({
    url: '/apiv1/login/confirm_delete'
  })
}
export const query_price = (data: QueryPrice) => {
  return service.post<{ amount_id: string }>({
    url: '/apiv1/fee/query_price',
    data
  })
}
export const country_list = () => {
  return service.get<CountryListRes>({
    url: '/apiv1/fee/country_list'
  })
}
export const payment = (data: QueryPay) => {
  return service.post<payItem>({
    url: '/apiv1/fee/payment',
    data
  })
}

export const getUserInfo = (client_id?: number) => {
  return service.post<IUserInfo>({
    url: '/apiv1/user/user_info',
    data: {
      client_id
    }
  })
}

export const getMyAiList = (params?: IAiListParams) => {
  return service.post<IAiList>({
    url: '/apiv1/ai/my_list',
    data: params
  })
}

export const getFollowList = (params?: IListParams) => {
  return service.post<IFollowList<IFollowListItem>>({
    url: '/apiv1/user/follow_list',
    data: params
  })
}

export const getFansList = (params: IListParams) => {
  return service.post<IFollowList<IFollowListItem>>({
    url: '/apiv1/user/fans_list',
    data: params
  })
}

export const getLoveList = (params: IListParams) => {
  return service.post<IFollowList<IAiListItem>>({
    url: '/apiv1/ai/like_list',
    data: params
  })
}

export const setLike = (params: { ai_id: number }) => {
  return service.post({
    url: '/apiv1/ai/set_like',
    data: params
  })
}

export const getPriceList = () => {
  return service.get<ICrystalPromotion[]>({
    url: '/apiv1/wallet/crystal_promotion_list'
  })
}

export const getGoldPrice = () => {
  return service.get<string>({
    url: '/apiv1/wallet/crystal_to_gold'
  })
}

export const getPriceCrystal = (params: ICrystalParams) => {
  return service.post<IGoldPriceId>({
    url: '/apiv1/wallet/query_price_crystal',
    data: params
  })
}

export const paymentCrystal = (params: { amount_id: string }) => {
  return service.post({
    url: '/apiv1/wallet/payment_crystal',
    data: params
  })
}

export const getFaq = () => {
  return service.post<IFaq>({
    url: '/apiv1/info/faq',
    data: {
      page: 1,
      limit: 9999
    }
  })
}

export const userFigure = (data: FigureListReq) => {
  return service.post<FigureListRes>({
    url: '/apiv1/user/image_manage',
    data
  })
}

export const checkCode = (data: { redeem_code: string }) => {
  return service.post({
    url: '/apiv1/user/checkRedeemCode',
    data
  })
}

export const retentionPopupInformation = () => {
  return service.post({
    url: '/apiv1/vip/is_show_retention_popup'
  })
}
