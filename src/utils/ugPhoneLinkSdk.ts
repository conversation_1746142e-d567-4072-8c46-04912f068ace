import { IUgPhoneLoginParams } from '@/api/login/types.ts'
import md5 from 'crypto-js/md5'
import { ugPhoneLogin } from '@/api/login'
import { ResultEnum } from '@/enums/httpEnum.ts'
// import 'https://uggame.wujialin.top/uggame-page/h5sdk/h5gamesdk.js'
import 'https://www.uggamer.com/uggame-page/h5sdk/h5gamesdk.js'
import { BOOL_NUMBER } from '@/constant'
import { useModal } from '@/hooks/useModal.ts'
import { i18n } from '@/utils/i18n.ts'

class UgPhoneLinkSdk {
  constructor() {
    const config = {
      saveImageResult: {
        success: (status: number) => {
          if (status === BOOL_NUMBER.YES) {
            useModal({ message: i18n.global.t('saveSuccessfully') })
          } else {
            useModal({ message: i18n.global.t('saveFailed') })
          }
        }
      }
    }
    ;(window as any).OG_H5_GAME_SDK.config(config)
  }
  getSign(param: IUgPhoneLoginParams | string = '', key = 'DVQZ9Mal0fUCWzt') {
    const arrSort = (para: IUgPhoneLoginParams) => {
      const keys = Object.keys(para).sort()
      return keys.reduce((result, key) => {
        result.push(key + '=' + encodeURIComponent(para[key as keyof IUgPhoneLoginParams]))
        return result
      }, [])
    }
    // MD5验签加密
    let md5Str
    if (typeof param === 'object' && param !== null) {
      md5Str = arrSort(param).join('&')
    } else {
      md5Str = param?.toString()
    }
    console.log('md5Str', md5Str + key)
    const md5Hash = md5(md5Str + key)
    return param === undefined || param === '' ? 'false' : md5Hash.toString()
  }

  async login(params: IUgPhoneLoginParams, paramsKey: string) {
    console.log(params)

    params.sign = this.getSign(params, paramsKey)
    params.key = paramsKey
    const { code, data } = await ugPhoneLogin(params)
    if (code === ResultEnum.SUCCESS) {
      return data
    } else {
      return undefined
    }
  }

  saveImage(url: string) {
    ;(window as any).OG_H5_GAME_SDK.saveImageResult()
    ;(window as any).JsAndroid.saveImage(url)
  }

  back() {
    try {
      localStorage.removeItem('ug-lang')
      localStorage.removeItem('access_token')
      localStorage.removeItem('login_id')
      localStorage.removeItem('userInformation')
      ;(window as any).JsAndroid.finishWebView()
    } catch (_) {
      ;(window as any).OG_H5_GAME_SDK.actionEvent({ action: 'back' })
    }
  }

  micAllow() {
    try {
      console.log(1)
      ;(window as any).JsAndroid.requestVideoPermission()
    } catch (_) {}
  }

  registerBack(func: () => void) {
    try {
      window.ug_backShowInterstitialAd = () => {
        console.log(2)
        func()
      }
    } catch (_) {}
  }

  unregisterBack() {
    try {
      window.ug_backShowInterstitialAd = () => {}
    } catch (_) {}
  }
}

export const ugPhoneLinkSdk = new UgPhoneLinkSdk()
