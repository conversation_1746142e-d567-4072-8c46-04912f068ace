/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'Home': RouteRecordInfo<'Home', '/', Record<never, never>, Record<never, never>>,
    '/[...all]': RouteRecordInfo<'/[...all]', '/:all(.*)', { all: ParamValue<true> }, { all: ParamValue<false> }>,
    '/AgentChat/[ai_id]': RouteRecordInfo<'/AgentChat/[ai_id]', '/AgentChat/:ai_id', { ai_id: ParamValue<true> }, { ai_id: ParamValue<false> }>,
    'AdvancedCreate': RouteRecordInfo<'AdvancedCreate', 'AdvancedCreate/:id?', Record<never, never>, Record<never, never>>,
    'agentBackground': RouteRecordInfo<'agentBackground', '/AgentCreate/AgentBackground', Record<never, never>, Record<never, never>>,
    'agentTag': RouteRecordInfo<'agentTag', '/AgentCreate/AgentTag', Record<never, never>, Record<never, never>>,
    'dialogueExample': RouteRecordInfo<'dialogueExample', '/AgentCreate/DialogueExample', Record<never, never>, Record<never, never>>,
    'DiyFigure': RouteRecordInfo<'DiyFigure', '/AgentCreate/DiyFigure', Record<never, never>, Record<never, never>>,
    'Drafts': RouteRecordInfo<'Drafts', '/AgentCreate/Drafts', Record<never, never>, Record<never, never>>,
    'FreeFigure': RouteRecordInfo<'FreeFigure', '/AgentCreate/FreeFigure', Record<never, never>, Record<never, never>>,
    'GenerateFigure': RouteRecordInfo<'GenerateFigure', 'GenerateFigure/:prompt_id', Record<never, never>, Record<never, never>>,
    'QuickCreate': RouteRecordInfo<'QuickCreate', '/AgentCreate/QuickCreate', Record<never, never>, Record<never, never>>,
    'SoundSetting': RouteRecordInfo<'SoundSetting', '/AgentCreate/SoundSetting', Record<never, never>, Record<never, never>>,
    '/AgentHomePage/[id]': RouteRecordInfo<'/AgentHomePage/[id]', '/AgentHomePage/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'agentMessage': RouteRecordInfo<'agentMessage', '/AgentMessage', Record<never, never>, Record<never, never>>,
    'Privacy': RouteRecordInfo<'Privacy', '/Agreement/Privacy', Record<never, never>, Record<never, never>>,
    'User': RouteRecordInfo<'User', '/Agreement/User', Record<never, never>, Record<never, never>>,
    '/Chat/[ai_id]': RouteRecordInfo<'/Chat/[ai_id]', '/Chat/:ai_id', { ai_id: ParamValue<true> }, { ai_id: ParamValue<false> }>,
    '/Chat/IntimacyStrategy': RouteRecordInfo<'/Chat/IntimacyStrategy', '/Chat/IntimacyStrategy', Record<never, never>, Record<never, never>>,
    '/Home/': RouteRecordInfo<'/Home/', '/Home', Record<never, never>, Record<never, never>>,
    'Member': RouteRecordInfo<'Member', '/MemberMall', Record<never, never>, Record<never, never>>,
    'mine': RouteRecordInfo<'mine', '/Mine', Record<never, never>, Record<never, never>>,
    '/Mine/Components/Cropper': RouteRecordInfo<'/Mine/Components/Cropper', '/Mine/Components/Cropper', Record<never, never>, Record<never, never>>,
    'crystal': RouteRecordInfo<'crystal', '/Mine/Crystal', Record<never, never>, Record<never, never>>,
    'EditImage': RouteRecordInfo<'EditImage', '/Mine/EditImage', Record<never, never>, Record<never, never>>,
    'follow': RouteRecordInfo<'follow', '/Mine/Follow', Record<never, never>, Record<never, never>>,
    '/Mine/Message': RouteRecordInfo<'/Mine/Message', '/Mine/Message', Record<never, never>, Record<never, never>>,
    '/Mine/MessageDetail': RouteRecordInfo<'/Mine/MessageDetail', '/Mine/MessageDetail', Record<never, never>, Record<never, never>>,
    'orderRecord': RouteRecordInfo<'orderRecord', '/Mine/OrderRecord', Record<never, never>, Record<never, never>>,
    'others': RouteRecordInfo<'others', '/Mine/Others', Record<never, never>, Record<never, never>>,
    'selfImage': RouteRecordInfo<'selfImage', '/Mine/SelfImage', Record<never, never>, Record<never, never>>,
    'WalletDetail': RouteRecordInfo<'WalletDetail', '/Mine/WalletDetail', Record<never, never>, Record<never, never>>,
    'Order': RouteRecordInfo<'Order', '/Order', Record<never, never>, Record<never, never>>,
    'Payment': RouteRecordInfo<'Payment', '/Payment', Record<never, never>, Record<never, never>>,
    '/Payment/components/PayPal': RouteRecordInfo<'/Payment/components/PayPal', '/Payment/components/PayPal', Record<never, never>, Record<never, never>>,
    '/Preference/': RouteRecordInfo<'/Preference/', '/Preference', Record<never, never>, Record<never, never>>,
    '/PropertyStore/': RouteRecordInfo<'/PropertyStore/', '/PropertyStore', Record<never, never>, Record<never, never>>,
    '/PropertyStore/BackPack': RouteRecordInfo<'/PropertyStore/BackPack', '/PropertyStore/BackPack', Record<never, never>, Record<never, never>>,
    'RankingList': RouteRecordInfo<'RankingList', '/RankingList', Record<never, never>, Record<never, never>>,
    '/RankingList/component/RankList': RouteRecordInfo<'/RankingList/component/RankList', '/RankingList/component/RankList', Record<never, never>, Record<never, never>>,
    'search': RouteRecordInfo<'search', '/Search', Record<never, never>, Record<never, never>>,
    'Set': RouteRecordInfo<'Set', '/Setting', Record<never, never>, Record<never, never>>,
    'Version': RouteRecordInfo<'Version', '/Setting/CheckVersion', Record<never, never>, Record<never, never>>,
    '/Setting/Components/Cropper': RouteRecordInfo<'/Setting/Components/Cropper', '/Setting/Components/Cropper', Record<never, never>, Record<never, never>>,
    'FAQ': RouteRecordInfo<'FAQ', '/Setting/FAQ', Record<never, never>, Record<never, never>>,
    'Information': RouteRecordInfo<'Information', '/Setting/Information', Record<never, never>, Record<never, never>>,
    'Invitation': RouteRecordInfo<'Invitation', '/Setting/Invitation', Record<never, never>, Record<never, never>>,
    'Logoff': RouteRecordInfo<'Logoff', '/Setting/Logoff', Record<never, never>, Record<never, never>>,
    'LogoffResult': RouteRecordInfo<'LogoffResult', '/Setting/LogoffResult', Record<never, never>, Record<never, never>>,
    'Preference': RouteRecordInfo<'Preference', '/Setting/Preference', Record<never, never>, Record<never, never>>,
    'task': RouteRecordInfo<'task', '/Task', Record<never, never>, Record<never, never>>,
  }
}
