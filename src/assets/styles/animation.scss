// from-bottom transition animation
.from-bottom-enter-active,
.from-bottom-leave-active {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.from-bottom-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.from-bottom-enter-to {
  transform: translateY(0);
  opacity: 1;
}

.from-bottom-leave-from {
  transform: translateY(0);
  opacity: 1;
}

.from-bottom-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.from-opacity-enter-active,
.from-opacity-leave-active {
  transition: all 0.3s ease;
}

.from-opacity-enter-from,
.from-opacity-leave-to {
  opacity: 0;
}

.from-scale-enter-active,
.from-scale-leave-active {
  transition: all 0.3s ease;
}

.from-scale-enter-from,
.from-scale-leave-to {
  transform: scale(0);
}

@keyframes overlay-background {
  0% {
    background: rgba(0, 0, 0, 0);
  }

  100% {
    background: rgba(0, 0, 0, 80%);
  }
}