import useAppStore from '@/stores/modules/app.ts'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useConfirm } from '@/hooks/useConfirm.ts'
import { useModal } from '@/hooks/useModal.ts'
import router from '@/router'
import { i18n } from '@/utils/i18n.ts'
import { POPUP_TYPE_NAME, usePopupMemoryStore } from '@/stores/modules/popupMemory.ts'
import { ugPhoneLinkSdk } from '@/utils/ugPhoneLinkSdk.ts'

export const usePaymentError = (code: ResultEnum, msg: string, popupType?: POPUP_TYPE_NAME, path?: string, id?: number) => {
  if (code === ResultEnum.COIN_DEFICIENCY) {
    const appStore = useAppStore()
    useModal({
      message: msg,
      duration: 500,
      onClose: () => {
        appStore.showCoinExchange = true
      }
    })
  } else if (code === ResultEnum.CRYSTAL_DEFICIENCY) {
    const appStore = useAppStore()
    const popupMemoryStore = usePopupMemoryStore()
    useConfirm({
      content: msg,
      cancelText: i18n.global.t('cancelButton'),
      confirmText: i18n.global.t('confirmButton'),
      onConfirm: () => {
        if (appStore.isUg) {
          window.OG_H5_GAME_SDK?.openLink('https://ugenie.net/landing')
          return
        }
        if (popupType && path) {
          console.log(popupType, path, id, 'sffff')
          popupMemoryStore.setPopupMemory(popupType, path, id)
        }
        router.push('/mine/crystal')
      }
    })
  } else {
    useModal({
      message: msg
    })
  }
}

export const useBackHistory = () => {
  const appStore = useAppStore()
  if (!window.history.state.back) {
    // 没有前置页面，跳转到默认页面或执行其他逻辑
    if (appStore.isUg) {
      ugPhoneLinkSdk.back()
    } else {
      router.push('/')
    }
  } else {
    // 有前置页面，执行返回操作
    router.back()
  }
}
