<script setup lang="ts">
import { cancelOrder, orderList } from '@/api/purchase'
import { OrderItem, OrderProductType } from '@/api/purchase/types.ts'
import { OrderStatusEnum } from '@/enums'
import Empty from '@/components/Empty.vue'
import { Pagination } from '@/api/mine/type.ts'
import { debounce } from 'lodash-es'

definePage({
  name: 'orderRecord'
})
const router = useRouter()
const active = ref<OrderProductType>('vip')
const loading = reactive<{
  vip: boolean
  crystal: boolean
  refreshing: boolean
  finished: boolean
  disabled: boolean
}>({
  vip: false,
  crystal: false,
  refreshing: false,
  finished: false,
  disabled: true
})
const list = reactive<{
  vip: OrderItem[]
  crystal: OrderItem[]
}>({
  vip: [],
  crystal: []
})
const show = ref<boolean>(false)
const cancel_order_id = ref<number>(0)

const pagination = reactive<Pagination>({
  page: 0,
  total: 0
})

const goWalletDetail = () => {
  router.push('/Mine/WalletDetail')
}

const onBeforeChange = (tab: OrderProductType) => {
  if (loading[tab]) {
    return false
  }
  return true
}

const onChange = (product_type: OrderProductType) => {
  loading[product_type] = true
  loading.finished = false
  list[product_type] = []
  pagination.total = 0
  pagination.page = 0
  getOrderList(product_type)
}

const getOrderList = (product_type: OrderProductType = 'vip') => {
  loading[product_type] = true
  orderList({
    product_type,
    limit: 10,
    page: ++pagination.page
  })
    .then((res) => {
      const { data, code } = res
      if (code === 200) {
        pagination.total = data.count
        if (pagination.total > list[product_type]?.length) {
          list[product_type].push(...data.list)
        } else {
          loading.finished = true
        }
      }
    })
    .finally(() => {
      loading[product_type] = false
      loading.refreshing = false
    })
}

getOrderList()

const onRefresh = (tab: OrderProductType) => {
  loading.finished = false
  loading[tab] = true
  getOrderList(tab)
}

const handleCancelOrder = (order_id: number) => {
  cancel_order_id.value = order_id
  show.value = true
}

const onCloseDialog = () => {
  show.value = false
}

const onCancelOrder = debounce(() => {
  cancelOrder({ order_id: cancel_order_id.value })
    .then((res) => {
      if (res.code === 200) {
        cancel_order_id.value = 0
        show.value = false
      }
    })
    .finally(() => {
      onChange(active.value)
    })
}, 400)
</script>

<template>
  <div
    class="container"
    ref="scrollContainerRef"
  >
    <div
      class="navbar flex-between-center"
      v-scroll-gradation-bg="{ completedHeight: 150, startHeight: 100 }"
    >
      <div
        class="flex-center-center"
        @click="$router.back()"
      >
        <SvgIcon
          icon-class="back"
          class="fsize-22"
        />
      </div>
      <div class="title">{{ $t('orderRecords') }}</div>
      <SvgIcon
        icon-class="order-list"
        class="fsize-22"
        @click="goWalletDetail"
      />
    </div>
    <div class="main">
      <van-tabs
        class="order-tabs"
        v-model:active="active"
        line-width="35%"
        :before-change="onBeforeChange"
        @change="onChange"
      >
        <van-tab
          :title="$t('subscribe')"
          name="vip"
        >
          <div
            v-if="list.vip.length || loading.vip"
            class="member-list"
          >
            <van-pull-refresh
              v-model="loading.refreshing"
              :disabled="loading.disabled"
              :pulling-text="$t('pullToRefresh')"
              :loading-text="$t('loading')"
              :loosing-text="$t('looseToRefresh')"
              @refresh="onRefresh('vip')"
            >
              <van-list
                v-model:loading="loading.vip"
                :finished="loading.finished"
                :loading-text="$t('loading')"
                :finished-text="$t('noMore')"
                @load="onRefresh('vip')"
              >
                <van-skeleton
                  :loading="!list.vip.length && loading.vip"
                  style="display: block; padding: 0; margin: 10px"
                >
                  <template #template>
                    <div class="order-item">
                      <van-skeleton-title title-width="80%" />
                      <van-divider />
                      <div class="info-item">
                        <span>{{ $t('orderNumber') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <div class="info-item">
                        <span>{{ $t('orderType') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <div class="info-item">
                        <span>{{ $t('creationTime') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <div class="info-item">
                        <span>{{ $t('paymentMethod') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <van-divider />
                      <div class="status skeleton-status">
                        <van-skeleton-title
                          title-width="20%"
                          class="pay-status"
                        />
                        <van-skeleton-paragraph
                          row-width="28%"
                          class="cancel-button"
                          round
                        />
                      </div>
                    </div>
                  </template>
                  <van-cell-group
                    v-for="order in list.vip"
                    :key="order.order_id"
                    class="order-item"
                    inset
                  >
                    <h2>{{ order.order_name }}</h2>
                    <van-divider />
                    <div class="info-item order-id-item">
                      <span>{{ $t('orderNumber') }}</span>
                      <p>{{ order.order_id }}</p>
                    </div>
                    <div class="info-item">
                      <span>{{ $t('orderType') }}</span>
                      <p>{{ order.order_type }}</p>
                    </div>
                    <div class="info-item">
                      <span>{{ $t('creationTime') }}</span>
                      <p>{{ order.create_time }}</p>
                    </div>
                    <div class="info-item">
                      <span>{{ $t('paymentMethod') }}</span>
                      <p>{{ order.pay_channel }}</p>
                    </div>
                    <van-divider />
                    <div class="status">
                      <h2>{{ $t(OrderStatusEnum[order.order_status]) }}</h2>
                      <!--订单处理中-->
                      <van-button
                        v-if="order.order_status === 0"
                        round
                        type="primary"
                        plain
                        size="small"
                        class="cancel-order-button"
                        @click="handleCancelOrder(order.order_id)"
                        >{{ $t('cancelOrder') }}
                      </van-button>
                      <span v-else>${{ order.order_amount }}</span>
                    </div>
                  </van-cell-group>
                </van-skeleton>
              </van-list>
            </van-pull-refresh>
          </div>
          <div
            v-else
            class="empty-box flex-center-center"
          >
            <Empty :text="$t('noMoreOrder')">
              <template #empty-img>
                <img
                  src="@/assets/images/mine/follow-empty.png"
                  alt="empty"
                />
              </template>
            </Empty>
          </div>
        </van-tab>
        <van-tab
          :title="$t('crystal')"
          name="crystal"
        >
          <div
            v-if="list.crystal.length || loading.crystal"
            class="crystal-list"
          >
            <van-pull-refresh
              v-model="loading.refreshing"
              :disabled="loading.disabled"
              :pulling-text="$t('pullToRefresh')"
              :loading-text="$t('loading')"
              :loosing-text="$t('looseToRefresh')"
              @refresh="onRefresh('crystal')"
            >
              <van-list
                v-model:loading="loading.crystal"
                :finished="loading.finished"
                :loading-text="$t('loading')"
                :finished-text="$t('noMore')"
                @load="onRefresh('crystal')"
              >
                <van-skeleton
                  :loading="!list.crystal.length && loading.crystal"
                  style="display: block; padding: 0; margin: 10px"
                >
                  <template #template>
                    <div class="order-item">
                      <van-skeleton-title title-width="80%" />
                      <van-divider />
                      <div class="info-item">
                        <span>{{ $t('orderNumber') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <div class="info-item">
                        <span>{{ $t('orderType') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <div class="info-item">
                        <span>{{ $t('creationTime') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <div class="info-item">
                        <span>{{ $t('paymentMethod') }}</span>
                        <van-skeleton-paragraph row-width="50%" />
                      </div>
                      <van-divider />
                      <div class="status skeleton-status">
                        <van-skeleton-title
                          title-width="20%"
                          class="pay-status"
                        />
                        <van-skeleton-paragraph
                          row-width="28%"
                          class="cancel-button"
                          round
                        />
                      </div>
                    </div>
                  </template>
                  <van-cell-group
                    v-for="order in list.crystal"
                    :key="order.order_id"
                    class="order-item"
                    inset
                  >
                    <h2>{{ order.order_name }}</h2>
                    <van-divider />
                    <div class="info-item">
                      <span>{{ $t('orderNumber') }}</span>
                      <p>{{ order.order_id }}</p>
                    </div>
                    <div class="info-item">
                      <span>{{ $t('orderType') }}</span>
                      <p>{{ order.order_type }}</p>
                    </div>
                    <div class="info-item">
                      <span>{{ $t('creationTime') }}</span>
                      <p>{{ order.create_time }}</p>
                    </div>
                    <div class="info-item">
                      <span>{{ $t('paymentMethod') }}</span>
                      <p>{{ order.pay_channel }}</p>
                    </div>
                    <van-divider />
                    <div class="status">
                      <h2>{{ $t(OrderStatusEnum[order.order_status]) }}</h2>
                      <!--订单处理中-->
                      <van-button
                        v-if="order.order_status === 0"
                        round
                        type="primary"
                        plain
                        size="small"
                        class="cancel-order-button"
                        @click="handleCancelOrder(order.order_id)"
                        >{{ $t('cancelOrder') }}
                      </van-button>
                      <span v-else>${{ order.order_amount }}</span>
                    </div>
                  </van-cell-group>
                </van-skeleton>
              </van-list>
            </van-pull-refresh>
          </div>
          <div
            v-else
            class="empty-box flex-center-center"
          >
            <Empty :text="$t('noMoreOrder')">
              <template #empty-img>
                <img
                  src="@/assets/images/mine/follow-empty.png"
                  alt="empty"
                />
              </template>
            </Empty>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <van-dialog
      v-model:show="show"
      :show-cancel-button="false"
      :show-confirm-button="false"
      class="cancel-order-dialog"
    >
      <div class="flex-center-center flex-column content">
        <h4 class="cancel-order-title">{{ $t('membershipCenter') }}</h4>
        <div class="cancel-order-content">
          <p>{{ $t('orderCancellationConfirm') }}</p>
        </div>
        <div class="btns flex-between-center">
          <div
            class="btn flex-center-center btn1"
            @click="onCloseDialog"
          >
            <span>
              {{ $t('closeButton') }}
            </span>
          </div>
          <div
            class="btn flex-center-center btn2"
            @click="onCancelOrder"
          >
            <span>
              {{ $t('cancelUnpaidOrder') }}
            </span>
          </div>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<style scoped lang="scss">
.container {
  position: relative;
  height: 100%;
  overflow: auto;
  background-repeat: no-repeat;
  background-position:
    top,
    right top;
  background-size: 100%, 35%;

  :deep(.van-tabs__nav) {
    background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);
  }

  :deep(.van-tab--active) {
    color: #fff;
  }

  :deep(.van-tabs__line) {
    width: calc(35% - 32px) !important;
    height: 1px !important;
    background: $livCoThemeColor;
  }

  :deep(.cancel-order-dialog) {
    padding: 10px 16px 24px;
  }
}

.navbar {
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 46px;
  padding: 0 16px;
  background-color: rgba(var(--van-nav-bar-background), 0.8);
  backdrop-filter: blur(10px);

  .title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }
}

.main {
  height: calc(100% - 46px);
  margin-top: 46px;

  :deep(.van-tabs) {
    height: 100%;

    .van-tabs__content {
      height: calc(100% - 46px);
      overflow: hidden auto;
    }
  }

  .member-list,
  .crystal-list {
    padding-top: 16px;

    :deep(.van-skeleton-paragraph) {
      justify-self: flex-end;
      margin-top: 0;
    }

    .order-item {
      padding: 16px;
      margin-bottom: 16px;

      h2 {
        margin-top: 0;
        font-size: 16px;
      }

      .info-item {
        display: grid;
        grid-template-rows: 1fr;
        grid-template-columns: auto auto;
        column-gap: 16px;
        width: 100%;
        margin-bottom: 16px;

        span {
          font-size: 14px;
          color: #adadad;
          white-space: nowrap;
        }

        p {
          justify-self: flex-end;
          margin: 0;
          font-size: 14px;
          text-align: end;
          word-break: break-all;
          white-space: pre-wrap;
        }
      }

      .skeleton-status {
        padding: 5px 0;

        .pay-status {
          height: 20px;
        }

        .cancel-button {
          height: 30px;
        }
      }

      .status {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h2 {
          margin-bottom: 0;
          font-size: 16px;
        }

        span {
          font-size: 16px;
          font-weight: 600;
          color: #de2e5c;
        }

        .cancel-order-button {
          padding: 0 16px;
          background: #444237;
        }
      }
    }
  }
}

.loading-box,
.empty-box {
  display: flex;
  flex-direction: column;
  padding: 120px 0;
  color: rgba(255, 255, 255, 50%);

  img {
    width: 240px;
  }
}

.cancel-order-title {
  font-size: 16px;
}

.cancel-order-content {
  font-size: 14px;
  line-height: 1.5;
}

.btns {
  width: 100%;
  padding-top: 20px;
}

.btn {
  width: 134px;
  height: 48px;
  padding: 0 16px;
  border-radius: 12px;

  span {
    font-size: 13px;
    text-align: center;
  }
}

.btn1 {
  color: #c1c1c1;
  background: #3d3c3c;
}

.btn2 {
  color: #fff;
  background: #ff35f2;
}
</style>
