<script setup lang="ts">
import { goods_list, promotion_list, query_price, retentionPopupInformation } from '@/api/mine/index'
import useUserStore from '@/stores/modules/user'
import { cardItem, goodItem } from '@/api/mine/type'
import { useModal } from '@/hooks/useModal'
import SvgIcon from '@/components/SvgIcon.vue'
import { eventReport, EventTypeEnum } from '@/api/eventReport'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { isEmpty } from 'lodash-es'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
dayjs.extend(duration)
definePage({
  name: 'Member',
  meta: {
    level: 2
  }
})
const { t } = useI18n()
const scrollContainerRef = ref()
const userStore = useUserStore()
userStore.getUserInfo()
const { userInformation } = toRefs(userStore)

const router = useRouter()
const currentVipType = ref(0)
const cardCurrent = ref(0)
const memberList = ref<goodItem[]>([])
const card = ref<cardItem[]>([])
const interest_list = ref([])
const price = ref('')
const product_id = ref('')
const isblackBg = ref(false)
const memberDiscountVisible = ref(false)
const countdownFunc = (date: string) => {
  // 解析目标时间字符串为 Day.js 对象
  // 使用 customParseFormat 插件确保正确解析 'YYYY-MM-DD HH:mm:ss' 格式
  const targetDate = dayjs(date, 'YYYY-MM-DD HH:mm:ss')

  // 获取当前时间
  const now = dayjs()

  // 计算差值（毫秒）
  const diffInMilliseconds = targetDate.diff(now)

  // 如果目标时间已过，返回 0
  if (diffInMilliseconds <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    }
  }

  // dayjs.extend(window.dayjs_plugin_duration);
  const duration = dayjs.duration(diffInMilliseconds)

  // 提取天、小时、分钟、秒
  // 注意：duration.hours(), duration.minutes(), duration.seconds() 返回的是不包含更高单位的部分
  // 例如，150 分钟是 2 小时 30 分钟，duration.hours() 返回 2，duration.minutes() 返回 30
  const days = Math.floor(duration.asDays())
  const hours = duration.hours()
  const minutes = duration.minutes()
  const seconds = duration.seconds()

  return {
    days: days,
    hours: hours,
    minutes: minutes,
    seconds: seconds
  }
}

function changeCardcurrent(i: number, item: cardItem) {
  cardCurrent.value = i
  interest_list.value = item.interest_list
  price.value = item.newpay_price
  product_id.value = item.id
}
function getGoodlist() {
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  goods_list()
    .then((res) => {
      if (res.code !== 200) return
      if (res.data.length === 0) return
      memberList.value = res.data
      promotion_list({ goods_id: memberList.value?.[0].id }).then((response) => {
        card.value = response.data
        interest_list.value = card.value?.[0].interest_list
        price.value = card.value?.[0].newpay_price
        product_id.value = card.value?.[0].id
      })
    })
    .finally(() => {
      close()
    })
}

function gobuy() {
  if (!product_id.value)
    return useModal({
      message: t('chooseCombo')
    })
  const { close } = useModal({
    message: t('loading'),
    loading: true,
    autoClose: false
  })
  let params = {
    product_type: 'vip',
    product_id: product_id.value,
    order_type: 'newpay'
  }
  query_price(params)
    .then((res) => {
      router.push({
        name: 'Payment',
        query: { amount_id: res.data.amount_id, price: price.value }
      })
      sessionStorage.setItem('buyType', 'Member')
    })
    .finally(() => {
      close()
    })
}

const retentionMsg = () => {}

function goRecord() {
  router.push({ name: 'orderRecord' })
}

function goback() {
  sessionStorage.getItem('buyType') === 'Member' ? router.push({ name: 'mine' }) : router.go(-1)
}
const userInfoObserver = ref<IntersectionObserver | null>(null)

function observerHandle() {
  // if (!document.querySelector('#userinfo')) return
  userInfoObserver.value = new IntersectionObserver(
    (entries) => {
      isblackBg.value = !entries[0].isIntersecting
    },
    {
      root: null,
      threshold: 0
    }
  )
  userInfoObserver.value.observe(document.querySelector('#userinfo'))
}
getGoodlist()
retentionMsg()
onMounted(() => {
  observerHandle()
  eventReport({
    event_type: EventTypeEnum.SHOW_BUY_VIP
  })
})
onUnmounted(() => {
  if (userInfoObserver.value) {
    userInfoObserver.value.disconnect()
    userInfoObserver.value = null
  }
})

onBeforeRouteLeave(async (_, __, next) => {
  // 也可以在这里阻止离开
  const { code, data } = await retentionPopupInformation()
  if (code === ResultEnum.SUCCESS) {
    if (isEmpty(data)) {
      next()
    } else {
      memberDiscountVisible.value = true
      next(false)
    }
  }
  next()
})
</script>
<template>
  <div
    class="container"
    :class="currentVipType === 3 ? 'super-member' : ''"
    ref="scrollContainerRef"
  >
    <img
      src="@/assets/images/mine/member-bgi.png"
      alt=""
      class="container-header-bg"
    />
    <img
      v-if="currentVipType !== 3"
      src="@/assets/images/mine/diamond-bgi.png"
      alt="diamond"
      class="diamond-bgi"
    />
    <img
      v-else
      src="@/assets/images/mine/stars-bg.png"
      alt="diamond"
      class="diamond-bgi"
    />
    <div
      class="navbar flex-between-center"
      v-scroll-gradation-bg="{ scrollElement: scrollContainerRef, completedHeight: 150, startHeight: 100 }"
    >
      <div
        class="flex-center-center"
        @click="goback"
      >
        <SvgIcon
          icon-class="back"
          class="fsize-24"
        />
      </div>

      <div
        class="title"
        @click="memberDiscountVisible = true"
      >
        VIP
      </div>
      <SvgIcon
        icon-class="history"
        class="fsize-24"
        @click="goRecord"
      />
    </div>

    <div class="main-container">
      <div
        class="userinfo flex-start-center"
        id="userinfo"
      >
        <van-image
          :src="userInformation?.avatar_url"
          alt="avatar"
        />
        <div class="flex-center-start flex-column textbox">
          <div class="name">{{ userInformation?.name }}</div>
          <div class="flex-start-center">
            <template v-if="userInformation.vip_count > 0">
              <img
                class="viplogo"
                src="@/assets/images/mine/vip-icon.png"
                alt=""
              />
              <span class="id">{{ userInformation.vip_info[0].vip_expiration_time.split(' ')[0] }} {{ t('maturity') }}</span>
            </template>
            <template v-else>
              <div class="sign">
                <span>{{ t('orderHistory') }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>
      <div
        class="content"
        :class="isblackBg ? 'blackBg' : ''"
      >
        <div
          class="pt-16 pb-12 cardbox"
          v-if="card.length > 0"
        >
          <div
            v-for="(item, i) in card"
            :key="i"
            class="card flex-center-center flex-column"
            :class="{ lightCard: cardCurrent == i, 'super-member': currentVipType === 3 }"
            @click="changeCardcurrent(i, item)"
          >
            <div class="flex-center-center flex-column flex-1">
              <div
                class="percent flex-center-center"
                v-if="item.discount > 0"
              >
                {{ item.discount }}% OFF
              </div>
              <div class="name">{{ item.unit }}</div>
              <div
                class="pt-8 pb-4 money"
                :class="currentVipType === 3 ? 'super-member' : ''"
              >
                ${{ item.newpay_price }}
              </div>

              <div
                class="account"
                v-if="item.discount > 0"
              >
                ${{ item.original_newpay_price }}
              </div>
            </div>
            <div
              class="clock"
              v-if="item.discount_deadline"
            >
              <IconSvgClockBlack class="fsize-16" />
              <div>
                {{ countdownFunc(item.discount_deadline).hours }}:{{ countdownFunc(item.discount_deadline).minutes }}:{{
                  countdownFunc(item.discount_deadline).seconds
                }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="title mt-9"
          v-if="card.length > 0 && interest_list.length > 0"
        >
          {{ t('noMembershipActivated') }}
        </div>
        <div class="pt-16 pb-24 vip">
          <div
            v-for="(item, i) in interest_list"
            :key="i"
            class="vipItem flex-start-center"
          >
            <van-image
              :src="item.icon"
              width="24"
              height="24"
              fit="cover"
              position="top"
            /><span class="gradient-text">{{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="bulingBox flex-center-center">
      <div
        class="buling flex-center-center"
        :class="{ 'super-member': currentVipType === 3 }"
        @click="gobuy"
      >
        <div class="btn-box">
          <span>{{ t('activateVIPBenefits') }} </span>
          <span class="price">${{ price }}</span>
        </div>
        <div class="light"></div>
      </div>
    </div>
    <MemberDiscountPopup v-model="memberDiscountVisible" />
  </div>
</template>
<style lang="scss" scoped>
@import 'src/assets/styles/variables';

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  background-color: #010101;

  .container-header-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 356px;
  }
}

.diamond-bgi {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 0;
  width: 174px;
}

.navbar {
  position: fixed;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 46px;
  padding: 0 16px;
  background: linear-gradient(180deg, rgba(24, 24, 24, 50%) 0%, rgba(24, 24, 24, 0%) 100%);

  .title {
    font-size: 16px;
    font-weight: 500;
  }
}

.userinfo {
  gap: 12px;
  padding: 62px 0 36px 16px;

  :deep(.van-image) {
    width: 48px;
    height: 48px;

    img {
      border: 1px solid #fff;
      border-radius: 50%;
    }

    :deep(.van-image__loading) {
      border: none;
      border-radius: 21px;

      .van-image__loading-icon {
        font-size: 24px;
      }
    }
  }

  .textbox {
    gap: 8px;

    .name {
      font-size: 16px;
      font-weight: 600;
    }

    .sign {
      height: 16px;
      padding: 0 6px;
      margin-right: 8px;
      font-size: 10px;
      color: rgba(255, 255, 255, 80%);
      background: rgba(31, 31, 31, 50%);
      border: 1px solid rgba(255, 255, 255, 45%);
      border-radius: 6px;

      &.active {
        width: 40px;
        height: 16px;
      }
    }

    .viplogo {
      height: 16px;
      margin-right: 8px;
    }

    .id {
      font-size: 12px;
      font-weight: 400;
      color: rgba(255, 255, 255, 50%);
    }
  }
}

.main-container {
  display: flex;
  flex-direction: column;
  height: calc(100% - 46px);
}

.content {
  flex: 1;
  padding-bottom: 90px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0%), #010101 30%, #010101 100%);
  backdrop-filter: blur(3px);
  border-radius: 24px 24px 0 0;
  transition: all 0.3s;

  &.blackBg {
    background-color: #121212;
  }

  .label {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 50%);
    text-align: center;
    transition: all 0.3s;
  }

  .lightLabel {
    position: relative;
    color: white;

    &::after {
      position: absolute;
      bottom: -17px;
      left: 50%;
      width: 32px;
      height: 2px;
      content: '';
      background-color: $themeColor;
      transform: translateX(-50%);
    }
  }

  .lightLabel.svip-label {
    &::after {
      background: linear-gradient(90deg, #4759ff 0%, #994aff 100%);
    }
  }

  .cardbox {
    display: flex;
    gap: 12px;
    padding-left: 16px;
    //overflow-x: auto;
    background: rgba(0, 0, 0, 30%);
    border-radius: 24px 24px 0 0;

    .card {
      position: relative;
      flex-shrink: 0;
      width: 166px;
      height: 132px;
      background: #020202;
      border: 2px solid #1a1a1a;
      border-radius: 16px;

      &:last-child {
        margin-right: 16px;
      }

      .name {
        font-size: 16px;
        font-weight: 600;
      }

      .percent {
        position: absolute;
        top: -4px;
        left: -2px;
        padding: 2px 8px;
        font-size: 12px;
        font-weight: 600;
        color: #fff;
        background: linear-gradient(270deg, #f9a546 0%, #ff6d3e 100%);
        border-radius: 8px 8px 8px 2px;
      }

      .money {
        font-family: 'DIN Bold', serif;
        font-size: 24px;
        font-weight: 800;
        color: #fff;
      }

      .account {
        font-family: 'DIN Light', serif;
        font-size: 13px;
        font-weight: 400;
        color: rgba(255, 255, 255, 30%);
        text-decoration: line-through;
      }

      .clock {
        padding: 4px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        column-gap: 1px;
        color: #292e37;
        width: 100%;
        font-size: 14px;
        border-radius: 0 0 12px 12px;
        background: #dceaff;
      }
    }

    .lightCard {
      background: #e5effa1f;
      border: 2px solid #dceaff;
    }
  }

  .title {
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 50%);
  }

  .vip {
    display: flex;
    flex-wrap: wrap;
    gap: 11px;
    padding: 16px 16px 24px;

    .vipItem {
      gap: 10px;
      width: 100%;
      padding: 12px 16px;
      font-size: 13px;
      font-weight: 500;
      background: #2229;
      border-radius: 12px;

      .gradient-text {
        background: linear-gradient(132.87deg, #fff4db 8.75%, #a0e1ff 86.97%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .tiaokuan {
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    color: rgba(255, 255, 255, 50%);
  }
}

.bulingBox {
  position: fixed;
  bottom: 0;
  left: 50%;
  width: 100%;
  height: 90px;
  background-color: $livCoBackColor;
  transform: translateX(-50%);

  .buling {
    position: relative;
    width: 343px;
    height: 52px;
    overflow: hidden;
    font-size: 16px;
    font-weight: 600;
    color: #161614;
    background: url('~@/assets/images/mine/buy-member.png') no-repeat;
    background-size: cover;
    border-radius: 16px;

    .btn-box {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 10;
      transform: translate(-50%, -50%);
    }

    .price {
      margin-left: 4px;
      font-size: 18px;
      font-weight: 800;
    }
  }

  .light {
    position: absolute;
    top: 50%;
    left: 0;
    z-index: 1;
    width: 79px;
    height: 55px;
    background: url('~@/assets/images/mine/light.png') no-repeat;
    background-size: contain;
    border-radius: 16px;
    transform: translateY(-50%);
    animation: moving 2s linear infinite;
  }
}

@keyframes moving {
  0% {
    left: -50px;
  }

  100% {
    left: 100vw;
  }
}

.super-member {
  &.lightCard {
    background-image: url('~@/assets/images/mine/money-card2.png') !important;
  }

  &.money {
    color: #8459ff !important;
  }

  &.container {
    background-image: url('~@/assets/images/mine/member-bg2.png');
  }

  &.buling {
    background: url('~@/assets/images/mine/superMember-btn.png') no-repeat;
    background-size: cover;
  }
}
</style>
