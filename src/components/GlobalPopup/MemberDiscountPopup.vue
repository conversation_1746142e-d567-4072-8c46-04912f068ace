<script setup lang="ts">
const show = defineModel<boolean>()
</script>

<template>
  <van-overlay
    v-model:show="show"
    class="flex-center-center flex-column overlay"
    z-index="9999"
    :close-on-click-overlay="false"
    :lock-scroll="false"
  >
    <Transition name="from-scale">
      <div
        v-show="show"
        class="flex-center-center flex-column"
      >
        <canvas id="canvas"></canvas>
        <div class="member-discount-popup">
          <div class="discount">
            <div class="flex-center-center flex-column rg-2">
              <div class="num">50%</div>
              <div class="off">OFF</div>
            </div>
            <div class="border"></div>
            <div class="text">低至￥0.1/周</div>
          </div>
          <div class="outside">
            <div>恭喜你被惊喜优惠券砸中</div>
            <div class="time">
              <IconSvgClock />
              <div class="hour time-num">23</div>
              <div>:</div>
              <div class="minute time-num">59</div>
              <div>:</div>
              <div class="second time-num">59</div>
            </div>
            <div class="btn">立即收下</div>
          </div>
        </div>
        <div
          class="close flex-center-center"
          @click="show = false"
        >
          <IconSvgClose />
        </div>
      </div>
    </Transition>
  </van-overlay>
</template>

<style scoped lang="scss">
.overlay {
  width: 100%;
  height: 100%;
  padding: 0 36px;
  background: rgba(0, 0, 0, 0.8);
}

.member-discount-popup {
  position: relative;
  width: 100%;
  padding: 0 16px;
  aspect-ratio: 303 / 251;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #101011;
  border: 1px solid #e5ebe84d;
  border-radius: 32px;

  @keyframes from-bottom-to-top {
    0% {
      transform: translateY(40%);
    }

    100% {
      transform: translateY(0);
    }
  }

  .discount {
    padding: 23px 30px;
    margin-top: -24px;
    display: flex;
    align-items: flex-start;
    color: $livCoTextColor;
    width: 100%;
    aspect-ratio: 271 / 160;
    transform: translateY(40%);
    background: url('@/assets/images/mine/discount-bg.png') no-repeat center / contain;
    animation: from-bottom-to-top 0.3s ease-out 0.3s forwards;

    .num {
      font-size: 26px;
      font-family: 'DIN Bold', serif;
    }

    .off {
      font-size: 14px;
      color: #6b2e0066;
      font-family: 'DIN Light', serif;
    }

    .border {
      margin-left: 23px;
      margin-right: 16px;
      border-left: 1px dashed #d7c5a9;
      height: 100%;
      width: 1px;
    }
    .text {
      margin-top: 11px;
      color: #2f2101;
    }
  }

  .outside {
    position: absolute;
    bottom: 0;
    left: -2px;
    width: 101%;
    aspect-ratio: 303 / 200;
    background: url('@/assets/images/mine/discount-bg2.png') no-repeat center / cover;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    padding: 38px 24px 24px;
    color: #fff;

    .time {
      display: flex;
      align-items: center;
      column-gap: 4px;

      .time-num {
        padding: 4px 3px;
        font-size: 13px;
        text-align: center;
        background: #46464a;
        color: #fff4db;
        border-radius: 4px;
        font-family: 'DIN Bold', serif;
      }
    }

    .btn {
      background: url('~@/assets/images/mine/buy-member.png') no-repeat center / cover;
      color: #161614;
      border-radius: 24px;
      width: 100%;
      height: 48px;
      line-height: 48px;
      text-align: center;
    }
  }
}

.close {
  margin-top: 24px;
  width: 34px;
  height: 34px;
  border: 2px solid #ffffff80;
  border-radius: 50%;
  font-size: 20px;
}
</style>
