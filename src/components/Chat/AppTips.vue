<script setup lang="ts">
import useAppStore from '@/stores/modules/app.ts'
const appStore = useAppStore()
const { t } = useI18n()

const touchParams = reactive({
  startClientY: 0,
  moveClientY: 0
})
const jumpToUg = () => {
  window.OG_H5_GAME_SDK?.openLink('https://ugenie.net/landing')
  // window.JsAndroid.clickRouter('/welfareCenter')
  appStore.showAppTips = false
}
const touchMove = () => {}

onMounted(() => {
  window.addEventListener('touchstart', (e) => {
    touchParams.startClientY = e.touches[0].pageY
  })
  window.addEventListener('touchmove', (e) => {
    touchParams.moveClientY = e.touches[0].pageY
  })
  window.addEventListener('touchend', () => {
    if (touchParams.startClientY - touchParams.moveClientY >= 20 && touchParams.moveClientY !== 0) {
      touchParams.startClientY = 0
      touchParams.moveClientY = 0
      appStore.showAppTips = false
    }
  })
})
onBeforeUnmount(() => {
  window.removeEventListener('touchmove', touchMove)
})
</script>

<template>
  <div class="app-tips">
    <div class="logo">
      <img
        src="/ugenie_icon.png"
        alt="ugenie"
      />
    </div>
    <div class="content">
      <div class="title">{{ t('onlineTips') }}</div>
      <div class="desc">{{ t('onlineDesc') }}</div>
    </div>
    <div
      class="btn"
      @click="jumpToUg"
    >
      {{ t('onlineJump') }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.app-tips {
  position: fixed;
  top: 12px;
  left: 12px;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 24px);
  min-height: 66px;
  padding: 12px;
  color: #fff;
  background: #18181882;
  backdrop-filter: blur(80px);
  border-radius: 12px;

  .logo {
    width: 42px;
    height: 42px;
    overflow: hidden;
    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    flex: 1;
    margin-left: 8px;

    .title {
      font-size: 13px;
      font-weight: 600;
      line-height: 16px;
    }

    .desc {
      margin-top: 2px;
      font-size: 10px;
      font-weight: 400;
      line-height: 14px;
    }
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 9px;
    font-size: 11px;
    color: $livCoTextColor;
    background: $livCoThemeColor;
    border-radius: 8px;
  }
}
</style>
