<script setup lang="ts">
const show = defineModel<boolean>()

const router = useRouter()
const goMember = () => {
  show.value = false
  router.push('/memberMall')
}
</script>

<template>
  <van-overlay
    v-model:show="show"
    class="flex-center-center flex-column overlay"
    z-index="9999"
    :close-on-click-overlay="false"
    :lock-scroll="false"
  >
    <Transition name="from-scale">
      <div
        v-show="show"
        class="flex-center-center flex-column"
      >
        <div class="keep-chat-member-popup">
          <img
            src="@/assets/images/chat/keep-chat-icon.png"
            alt="icon"
            width="170"
            class="icon"
          />
          <div class="fsize-18 mt-24">开通会员继续聊</div>
          <div class="fsize-13 op-text">您已达每日会话数上限,开通会员可享受无限制聊天</div>
          <div
            class="btn"
            @click="goMember"
          >
            去开通
          </div>
        </div>
        <div
          class="close flex-center-center"
          @click="show = false"
        >
          <IconSvgClose />
        </div>
      </div>
    </Transition>
  </van-overlay>
</template>

<style scoped lang="scss">
.overlay {
  width: 100%;
  height: 100%;
  padding: 0 32px;
  background: rgba(0, 0, 0, 0.8);
}

.keep-chat-member-popup {
  width: 100%;
  padding: 0 24px;
  aspect-ratio: 311 / 303;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url('@/assets/images/chat/keep-chat-bg.png') no-repeat center / cover;

  .icon {
    margin-top: -42px;
  }

  .op-text {
    margin-top: 8px;
    text-align: center;
    color: #ffffffb2;
  }

  .btn {
    width: 100%;
    height: 48px;
    margin-top: 32px;
    line-height: 48px;
    text-align: center;
    background: $livCoThemeColor;
    color: $livCoTextColor;
    box-shadow: 0 2px 12px 0 #ccd8f14d;
    border-radius: 24px;
  }
}

.close {
  margin-top: 24px;
  width: 34px;
  height: 34px;
  border: 2px solid #ffffff80;
  border-radius: 50%;
  font-size: 20px;
}
</style>
