<script setup lang="ts">
import useUserStore from '@/stores/modules/user.ts'
import { ForwardAddressEnum } from '@/api/eventReport'
import { getCrystal, getCrystalCode } from '@/api/chat'
import { ResultEnum } from '@/enums/httpEnum.ts'
import { useModal } from '@/hooks/useModal.ts'
import useAppStore from '@/stores/modules/app.ts'
import { Ref } from 'vue'
import { useAdStore } from '@/stores/modules/ad.ts'

const { t } = useI18n()
const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const adStore = useAdStore()
const aiID = inject<Ref<number>>('aiID')
const purchaseCrystal = async () => {
  if (appStore.isAndroid) {
    let crystalCode = ''
    const { code, data } = await getCrystalCode({
      tag: 'get_crystal'
    })
    if (code === ResultEnum.SUCCESS) {
      crystalCode = data
    }
    adStore
      .showCheckAdv('get_crystal', {
        aiID: aiID.value,
        trigger_position: '领取水晶'
      })
      .then(async () => {
        const { code } = await getCrystal({ code: crystalCode })
        if (code === ResultEnum.SUCCESS) {
          useModal({
            message: t('getSuccessfully'),
            duration: 3000
          })
          userStore.getUserInfo()
        }
      })
  } else {
    if (appStore.isUg) {
      window.OG_H5_GAME_SDK?.openLink('https://ugenie.net/landing')
      return
    }
    sessionStorage.setItem('crystal_front_address', ForwardAddressEnum.CHAT_PAGE)
    router.push({ name: 'crystal' })
  }
}
</script>

<template>
  <div class="crystal-container">
    <div class="balance flex-between-center">
      <div class="crystal">
        <img
          src="@/assets/images/crystal.png"
          alt=""
        />
      </div>
      <div class="num-size">{{ userStore.userInformation.crystal_amount }}</div>
      <SvgIcon
        @click="purchaseCrystal"
        icon-class="plus"
        class="fsize-22 ml-8"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.crystal-container {
  width: fit-content;
  padding-left: 11px;
}

.num-size {
  font-size: 12px;
}

.balance {
  height: 22px;
  font-size: 14px;
  background: #1a1a1a;
  border-radius: 0 8px 8px 0;

  .crystal {
    display: flex;
    align-items: center;
    height: 22px;
    margin-right: -10px;
    transform: translateX(-50%);

    img {
      top: 50%;
      display: block;
      width: 32px;
      height: 32px;
    }
  }
}
</style>
