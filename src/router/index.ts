import { createRouter, createWebHistory } from 'vue-router/auto'
import { existLanguages, locale } from '@/utils/i18n'
import useAppStore from '@/stores/modules/app'
import useLoadingStore from '@/stores/modules/loading'
import 'nprogress/nprogress.css'

import type { EnhancedRouteLocation } from './types'
import useRouteCacheStore from '@/stores/modules/routeCache'
import useUserStore from '@/stores/modules/user.ts'
import { ugPhoneLinkSdk } from '@/utils/ugPhoneLinkSdk.ts'
import { MQTT_LOCALSTORAGE_KEY } from '@/constant'
import { isEmpty } from 'lodash-es'
import useNotifyStore from '@/stores/modules/notification.ts'
import { TokenKey } from '@/utils/auth.ts'
import useWs from '@/hooks/useWs.ts'
import { abTest } from '@/api/login'
import { AbTestEnum } from '@/api/login/types.ts'
import { getDeviceFingerprint } from '@/utils/tools.ts'
const openList = ['/agreement/privacy', '/agreement/user', 'setting/logoff']
const whiteList = ['/login', '/agreement/privacy', '/agreement/user', '/setting/logoff', '/rankinglist', '/chat', '/agenthomepage', '/preference', '/']
const ugPhoneLinkList = ['/chat']

const previousRoutePath = ref('')
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_APP_PUBLIC_PATH),
  extendRoutes: (routes) => routes
})

const checkWhiteList = (path: string) => {
  return whiteList.some((item) => {
    if (item === '/') {
      return path.toLowerCase() === item.toLowerCase()
    } else {
      return path.toLowerCase().startsWith(item.toLowerCase())
    }
  })
}

router.beforeEach(async (to: EnhancedRouteLocation, from, next) => {
  // 如果在openList里面，直接跳转 不执行下面
  const routeCacheStore = useRouteCacheStore()

  routeCacheStore.addRoute(to)
  if (openList.includes(to.path)) {
    next()
    return
  }
  if (!localStorage.getItem('browserId')) {
    await getDeviceFingerprint()
  }
  if (to.path.includes('/chat')) {
    const loading = useLoadingStore()
    loading.show()
  }
  const userStore = useUserStore()
  const appStore = useAppStore()
  const notifyStore = useNotifyStore()
  let hadUgphoneLink = false
  ugPhoneLinkList.forEach((item) => {
    if (to.path.toLowerCase().includes(item.toLowerCase())) {
      hadUgphoneLink = true
    }
  })
  if (hadUgphoneLink) {
    if (to.query.isLivco) {
      appStore.isUg = true
      const queryLang = to.query.lang as string
      localStorage.setItem('ug-lang', queryLang)
      const lang = existLanguages.includes(queryLang) ? queryLang : 'en'
      locale.value = lang
      appStore.language = lang
      localStorage.setItem('language', lang)
      const params = {
        small_id: to.query.small_id ? (to.query.small_id as string) : '',
        game_id: to.query.game_id ? (to.query.game_id as string) : '',
        timestamp: to.query.timestamp ? (to.query.timestamp as string) : '',
        user_id: to.query.user_id ? (to.query.user_id as string) : '',
        token: to.query.token ? (to.query.token as string) : '',
        screen_type: to.query.screen_type ? (to.query.screen_type as string) : '',
        lang: lang ? (lang as string) : '',
        ugphone_user_id: to.query.ugphone_user_id ? (to.query.ugphone_user_id as string) : '',
        ug_visitor_id: to.query.ug_visitor_id ? (to.query.ug_visitor_id as string) : ''
      }
      const data = await ugPhoneLinkSdk.login(params, to.query.key ? (to.query.key as string) : '')
      if (data) {
        userStore.saveLoginInfo(data)
        localStorage.setItem('firstIn', '1')
        next()
        return
      } else {
        appStore.isUg = false
      }
    }
  }
  if (to.query.ugphone) {
    if (localStorage.getItem(TokenKey)) {
      next('/')
      return
    }
    sessionStorage.setItem('is_ugphone', 'ugphone')
  }
  if (!localStorage.getItem('firstIn')) {
    const { data } = await abTest([AbTestEnum.HOMEPAGE])
    localStorage.setItem('firstIn', '1')
    if (data[AbTestEnum.HOMEPAGE].is_first_begin) {
      next('/preference')
    }
  }
  if (!userStore.isLogin && !checkWhiteList(to.path)) {
    appStore.showLogin = true
    appStore.loginRedirectPath = to.path
    console.log(to, from)
    if (checkWhiteList(from.path)) {
      next(from.path)
      return
    } else {
      next('/')
      return
    }
  }

  if (!userStore.isLogin && checkWhiteList(to.path)) {
    appStore.loginRedirectPath = ''
  }

  const sessionNotification = JSON.parse(sessionStorage.getItem('livco_notification'))
  const mqtt_config = JSON.parse(localStorage.getItem(MQTT_LOCALSTORAGE_KEY) ?? '{}')
  if (!isEmpty(mqtt_config) && userStore.isLogin) {
    if (!appStore.isUg) {
      useWs.connect()
      notifyStore.initNoticeAndPoster(!sessionNotification)
    }
  }
  console.log(to, from, 'to from')
  previousRoutePath.value = from.fullPath
  next()
})

router.afterEach(() => {
  // NProgress.done()
  const loading = useLoadingStore()
  // 模拟延迟消除 loading（避免闪一下）
  loading.hide()
})

export default router
export { previousRoutePath }
