{"name": "ugenie", "private": true, "version": "1.0.0", "author": "yorenz", "type": "module", "scripts": {"dev": "vite --open", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preinstall": "npx only-allow pnpm", "preview": "vite preview", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "commit": "echo 若需要更新版本请执行pnpm run update-version && git add . && git-cz", "prepare": "husky"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix --allow-empty-input"], "*.{scss,css}": ["prettier --write", "stylelint --fix --allow-empty-input"], "*.md": ["prettier --write"]}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.5.1", "@vant/touch-emulator": "^1.4.0", "@vant/use": "^1.6.0", "@vueuse/components": "^10.11.1", "@vueuse/core": "^10.9.0", "animejs": "^3.2.2", "axios": "^1.7.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.18", "gsap": "^3.13.0", "howler": "^2.2.4", "html2canvas": "^1.4.1", "hume": "0.9.8", "live2d-motionsync": "^0.0.4", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "marked": "^14.1.0", "mqtt": "^5.10.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "pixi-live2d-display": "^0.4.0", "pixi.js": "6.4.2", "qrcode": "^1.5.4", "recorder-core": "^1.3.24102001", "rollup-plugin-visualizer": "^5.12.0", "spark-md5": "^3.0.2", "swiper": "^11.1.9", "vant": "^4.9.0", "vconsole": "^3.15.1", "vue": "^3.4.21", "vue-cropper": "^0.6.5", "vue-i18n": "^9.13.1", "vue-router": "^4.3.2"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@intlify/unplugin-vue-i18n": "^4.0.0", "@types/animejs": "^3.1.12", "@types/crypto-js": "^4.2.2", "@types/howler": "^2.2.11", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.7", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.10", "@types/spark-md5": "^3.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-basic-ssl": "1.1.0", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "commitizen": "^4.3.0", "cropperjs": "^1.6.2", "cz-git": "^1.9.1", "eslint": "^8.55.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.19.2", "husky": "^9.0.11", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "postcss-mobile-forever": "^4.1.5", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "qs": "^6.12.1", "sass": "^1.77.1", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.3.0", "stylelint-config-recommended-scss": "^13.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.0.0", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.5", "unplugin-icons": "^22.2.0", "unplugin-vue-components": "^0.26.0", "unplugin-vue-router": "^0.8.6", "vite": "^5.2.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock-dev-server": "^1.5.0", "vite-plugin-sitemap": "^0.5.3", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-devtools": "^7.3.7", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.0.6"}}