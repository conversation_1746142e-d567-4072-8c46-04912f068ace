<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>UGenie | your virtual pal</title>
    <meta
      name="description"
      content="Chat with your AI companion anytime, anywhere, and experience fantastic interactive stories. These highly anthropomorphic chatbots will listen to you, understand you, and remember you. Free to use"
    />
    <meta
      name="keywords"
      content="UGenie, UGenie, chat AI,  AI chat, chatbot, companion, AI friends, AI companion, AI character, AI roleplay, AI free, AI social tool, interactive stories, anime, virtual idol, live2d, L2D"
    />
    <meta
      property="og:type"
      content="website"
    />
    <meta
      property="og:image"
      content="/ugenie_icon.png"
    />
    <meta
      property="og:description"
      content="Chat with your AI companion anytime, anywhere, and experience fantastic interactive stories. These highly anthropomorphic chatbots will listen to you, understand you, and remember you. Free to use"
    />
    <meta
      property="og:title"
      content="UGenie | your virtual pal"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <meta
      name="apple-mobile-web-app-capable"
      content="yes"
    />
    <meta
      name="mobile-web-app-capable"
      content="yes"
    />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black"
    />
    <meta
      name="apple-mobile-web-app-title"
      content="UGenie"
    />
    <link
      rel="apple-touch-icon"
      href="/setting/pwa.png"
    />
    <link
      rel="icon"
      type="image/svg+xml"
      href="/ugenie_icon.png"
      sizes="64x64"
    />
    <link
      rel="preload"
      href="/font/Roboto-Medium.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/DIN-Bold.otf"
      as="font"
      type="font/otf"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/DIN-Light.otf"
      as="font"
      type="font/otf"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/IBMPlexSerif-Medium.8b682050.ttf"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/Roboto-Regular.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/Roboto-Bold.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/Alibaba_PuHuiTi_2.0_105_Heavy_105_Heavy.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="/font/RammettoOne-Regular.ttf"
      as="font"
      type="font/ttf"
      crossorigin
    />
    <style>
      @font-face {
        /* 重命名字体名 */
        font-family: 'RammettoOne';
        font-style: normal;
        font-weight: 400;

        /* 引入字体 */
        src: url('/font/RammettoOne-Regular.ttf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: 'DIN Bold';
        font-style: normal;
        font-weight: 600;

        /* 引入字体 */
        src: url('/font/DIN-Bold.otf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: 'DIN Light';
        font-style: normal;
        font-weight: 600;

        /* 引入字体 */
        src: url('/font/DIN-Light.otf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: Roboto;
        font-style: normal;
        font-weight: 500;

        /* 引入字体 */
        src: url('/font/Roboto-Medium.ttf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: Roboto;
        font-style: normal;
        font-weight: normal;

        /* 引入字体 */
        src: url('/font/Roboto-Regular.ttf');
        font-display: swap;
      }

      @font-face {
        /* 重命名字体名 */
        font-family: Roboto;
        font-style: normal;
        font-weight: 600;

        /* 引入字体 */
        src: url('/font/Roboto-Bold.ttf');
        font-display: swap;
      }

      @font-face {
        font-family: 'AlibabaPuHuiTi Heavy';
        font-style: normal;
        font-weight: 600;
        src: url('/font/Alibaba_PuHuiTi_2.0_105_Heavy_105_Heavy.woff2');
        font-display: swap;
      }

      @font-face {
        font-family: 'IBMSerif';
        font-style: normal;
        font-weight: 600;
        src: url('/font/IBMPlexSerif-Medium.8b682050.ttf');
        font-display: swap;
      }

      #initial-loading {
        position: fixed;
        inset: 0;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #fff;
        background: black;
      }


      .gl-container {
          --uib-size: 40px;
          --uib-color: rgb(235, 223, 172);
          --uib-speed: 1.5s;
          --global-dot-size: calc(var(--uib-size) * 0.17);

          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: var(--uib-size);
          height: var(--uib-size);
          animation: smoothRotate calc(var(--uib-speed) * 1.8) linear infinite;
      }

      .global-dot {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          width: 100%;
          height: 100%;
          animation: rotate var(--uib-speed) ease-in-out infinite;
      }

      .global-dot::before {
          width: var(--global-dot-size);
          height: var(--global-dot-size);
          content: '';
          background-color: var(--uib-color);
          border-radius: 50%;
          transition: background-color 0.3s ease;
      }

      .global-dot:nth-child(2),
      .global-dot:nth-child(2)::before {
          animation-delay: calc(var(--uib-speed) * -0.835 * 0.5);
      }

      .global-dot:nth-child(3),
      .global-dot:nth-child(3)::before {
          animation-delay: calc(var(--uib-speed) * -0.668 * 0.5);
      }

      .global-dot:nth-child(4),
      .global-dot:nth-child(4)::before {
          animation-delay: calc(var(--uib-speed) * -0.501 * 0.5);
      }

      .global-dot:nth-child(5),
      .global-dot:nth-child(5)::before {
          animation-delay: calc(var(--uib-speed) * -0.334 * 0.5);
      }

      .global-dot:nth-child(6),
      .global-dot:nth-child(6)::before {
          animation-delay: calc(var(--uib-speed) * -0.167 * 0.5);
      }

      @keyframes rotate {
          0% {
              transform: rotate(0deg);
          }

          65%,
          100% {
              transform: rotate(360deg);
          }
      }

      @keyframes smoothRotate {
          0% {
              transform: rotate(0deg);
          }

          100% {
              transform: rotate(360deg);
          }
      }

      @keyframes run {
          0% {
              transform: rotate(0);
          }

          100% {
              transform: rotate(360deg);
          }
      }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.13.0/lottie.min.js" integrity="sha512-uOtp2vx2X/5+tLBEf5UoQyqwAkFZJBM5XwGa7BfXDnWR+wdpRvlSVzaIVcRe3tGNsStu6UMDCeXKEnr4IBT8gA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>    <script>
      ;(function () { document.documentElement.classList.toggle('dark', true) })() /** * // const prefersDark = window.matchMedia &&
window.matchMedia('(prefers-color-scheme: dark)').matches * // const setting = localStorage.getItem('vueuse-color-scheme') || 'auto' * // if (setting === 'dark'
|| (prefersDark && setting !== 'light')) */
    </script>
  </head>
  <body>
    <div id="initial-loading">
      <div class="loading-box" id="lotties">
      </div>
    </div>
    <div id="app"></div>
    <script
      type="module"
      src="/src/main.ts"
    ></script>

    <script src="/setting/live2dcubismcore.min.js"></script>
    <script src="/setting/live2d.min.js"></script>
    <script>
      const loadingAnimation = lottie.loadAnimation({
        container: document.getElementById('lotties'), // Required
        path: './lottie/loading.json', // Required
        renderer: 'svg', // Required
        loop: true, // Optional
        autoplay: true // Optional
      })
    </script>
    <noscript> This website requires JavaScript to function properly. Please enable JavaScript to continue. </noscript>
  </body>
</html>
